"""
Enhanced stock data retrieval system with Context7-inspired improvements.
Features robust error handling, configuration management, data validation, and modular design.
"""

import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Optional, Any
from pathlib import Path
import sys
import argparse

# Import our enhanced modules
from config import get_config, set_config, StockConfig
from stock_data_fetcher import StockDataFetcher, FetchResult
from stock_analyzer import StockAnalyzer, AnalysisResult
from data_validator import DataValidator, ValidationResult
from utils import (
    setup_logging, log_with_context, format_duration, format_number,
    validate_date_range, create_output_directory, save_dataframe_multiple_formats,
    calculate_progress_eta, create_progress_bar, get_memory_usage
)


class EnhancedStockAnalysisSystem:
    """
    Main class for the enhanced stock analysis system.
    Coordinates data fetching, validation, analysis, and output.
    """
    
    def __init__(self, config: Optional[StockConfig] = None):
        """
        Initialize the enhanced stock analysis system.
        
        Args:
            config: Configuration object. If None, loads from default sources.
        """
        # Set up configuration
        if config:
            set_config(config)
        self.config = get_config()
        
        # Set up logging
        self.logger = setup_logging(self.config.logging)
        self.logger.info("Enhanced Stock Analysis System initialized")
        
        # Initialize components
        self.fetcher = StockDataFetcher(self.config.api)
        self.analyzer = StockAnalyzer(self.config.analysis)
        self.validator = DataValidator(self.config.validation)
        
        # Session tracking
        self.session_start = datetime.now()
        self.session_stats = {
            'stocks_processed': 0,
            'stocks_successful': 0,
            'validation_errors': 0,
            'analysis_results': 0
        }
    
    def test_data_retrieval(self, stock_codes: Optional[List[str]] = None, 
                          year: int = 2024, month: int = 12) -> bool:
        """
        Test stock data retrieval functionality with sample stocks.
        
        Args:
            stock_codes: List of stock codes to test. If None, uses default test codes.
            year: Year for test data
            month: Month for test data
            
        Returns:
            True if test successful, False otherwise
        """
        if stock_codes is None:
            stock_codes = self.config.test_stock_codes
        
        self.logger.info(f"Testing data retrieval with {len(stock_codes)} stocks")
        
        try:
            # Validate and format dates
            start_date = f"{year}-{month:02d}-01"
            try:
                days_in_month = pd.Timestamp(start_date).days_in_month
                end_date = f"{year}-{month:02d}-{days_in_month:02d}"
            except:
                end_date = f"{year}-{month:02d}-28"
            
            start_date, end_date = validate_date_range(start_date, end_date)
            
            # Convert to API format (YYYYMMDD)
            api_start = start_date.replace('-', '')
            api_end = end_date.replace('-', '')
            
            self.logger.info(f"Test date range: {start_date} to {end_date}")
            
            successful_tests = 0
            
            for i, stock_code in enumerate(stock_codes):
                self.logger.info(f"Testing stock {stock_code} ({i+1}/{len(stock_codes)})")
                
                # Fetch data
                result = self.fetcher.get_stock_data(stock_code, api_start, api_end)
                
                if result.success:
                    # Validate data
                    validation = self.validator.validate_stock_data(result.data, stock_code)
                    
                    if validation.is_valid:
                        self.logger.info(
                            f"✓ Stock {stock_code}: {len(result.data)} rows, "
                            f"validation passed"
                        )
                        successful_tests += 1
                    else:
                        self.logger.warning(
                            f"✗ Stock {stock_code}: Validation failed - {validation.errors}"
                        )
                else:
                    self.logger.warning(f"✗ Stock {stock_code}: Fetch failed - {result.error}")
            
            success_rate = successful_tests / len(stock_codes) * 100
            self.logger.info(f"Test completed: {successful_tests}/{len(stock_codes)} successful ({success_rate:.1f}%)")
            
            return success_rate >= 50  # Consider test successful if at least 50% pass
            
        except Exception as e:
            self.logger.error(f"Test failed with exception: {e}")
            return False
    
    def get_top_amplitude_stocks(self, year: int = 2024, month: int = 12, 
                               max_stocks: Optional[int] = None) -> pd.DataFrame:
        """
        Get top amplitude stocks for the specified period.
        
        Args:
            year: Year for analysis
            month: Month for analysis
            max_stocks: Maximum number of stocks to process (None for all)
            
        Returns:
            DataFrame with top amplitude stocks
        """
        self.logger.info(f"Starting amplitude analysis for {year}-{month:02d}")
        
        try:
            # Validate date range
            current_date = datetime.now()
            target_date = datetime(year, month, 1)
            
            if target_date > current_date:
                self.logger.warning(f"Target date {year}-{month} is in future, adjusting to previous month")
                if current_date.month == 1:
                    year = current_date.year - 1
                    month = 12
                else:
                    year = current_date.year
                    month = current_date.month - 1
            
            # Get stock list
            self.logger.info("Fetching stock list...")
            stock_list_result = self.fetcher.get_stock_list()
            
            if not stock_list_result.success:
                self.logger.error(f"Failed to fetch stock list: {stock_list_result.error}")
                return pd.DataFrame()
            
            stock_list = stock_list_result.data
            all_stocks = stock_list['code'].tolist()
            
            # Limit stocks if specified
            if max_stocks:
                all_stocks = all_stocks[:max_stocks]
            
            self.logger.info(f"Processing {len(all_stocks)} stocks")
            
            # Prepare date range
            start_date = f"{year}-{month:02d}-01"
            try:
                days_in_month = pd.Timestamp(start_date).days_in_month
                end_date = f"{year}-{month:02d}-{days_in_month:02d}"
            except:
                end_date = f"{year}-{month:02d}-28"
            
            start_date, end_date = validate_date_range(start_date, end_date)
            api_start = start_date.replace('-', '')
            api_end = end_date.replace('-', '')
            
            self.logger.info(f"Analysis period: {start_date} to {end_date}")
            
            # Fetch data for all stocks
            def progress_callback(completed, total, stock_code, success):
                if completed % 50 == 0 or completed == total:
                    progress_bar = create_progress_bar(completed, total)
                    eta = calculate_progress_eta(self.session_start, completed, total)
                    eta_str = f", ETA: {eta}" if eta else ""
                    self.logger.info(f"Progress: {progress_bar} ({completed}/{total}){eta_str}")
            
            stock_data_results = self.fetcher.get_batch_stock_data(
                all_stocks, api_start, api_end, progress_callback=progress_callback
            )
            
            # Separate successful results
            successful_data = {}
            for stock_code, result in stock_data_results.items():
                if result.success and result.data is not None and not result.data.empty:
                    successful_data[stock_code] = result.data
            
            self.logger.info(f"Successfully fetched data for {len(successful_data)} stocks")
            
            if not successful_data:
                self.logger.warning("No successful data fetches")
                return pd.DataFrame()
            
            # Validate data
            self.logger.info("Validating stock data...")
            validation_results = self.validator.validate_batch(successful_data)
            
            # Filter valid data
            valid_data = {}
            for stock_code, validation in validation_results.items():
                if validation.is_valid and validation.cleaned_data is not None:
                    valid_data[stock_code] = validation.cleaned_data
            
            validation_summary = self.validator.get_validation_summary(validation_results)
            self.logger.info(f"Validation summary: {validation_summary}")
            
            if not valid_data:
                self.logger.warning("No valid data after validation")
                return pd.DataFrame()
            
            # Create stock name mapping
            stock_names = {}
            for _, row in stock_list.iterrows():
                stock_names[row['code']] = row['name']
            
            # Analyze stocks
            self.logger.info("Analyzing stock amplitudes...")
            analysis_results = self.analyzer.analyze_batch(valid_data, stock_names)
            
            if not analysis_results:
                self.logger.warning("No analysis results generated")
                return pd.DataFrame()
            
            # Convert to DataFrame
            results_data = []
            for result in analysis_results:
                results_data.append({
                    '股票代码': result.stock_code,
                    '股票名称': result.stock_name,
                    '日均振幅(%)': round(result.avg_amplitude, 2),
                    '交易天数': result.trading_days,
                    '波动率(%)': round(result.metrics.get('volatility', 0), 2),
                    '平均价格': round(result.metrics.get('avg_price', 0), 2),
                    '总收益率(%)': round(result.metrics.get('total_return', 0), 2)
                })
            
            result_df = pd.DataFrame(results_data)
            
            # Log analysis summary
            analysis_summary = self.analyzer.get_analysis_summary(analysis_results)
            self.logger.info(f"Analysis summary: {analysis_summary}")
            
            # Update session stats
            self.session_stats.update({
                'stocks_processed': len(all_stocks),
                'stocks_successful': len(successful_data),
                'validation_errors': len(validation_results) - len(valid_data),
                'analysis_results': len(analysis_results)
            })
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"Analysis failed: {e}", exc_info=True)
            return pd.DataFrame()
    
    def save_results(self, results_df: pd.DataFrame, output_dir: Optional[str] = None) -> Dict[str, str]:
        """
        Save analysis results in multiple formats.
        
        Args:
            results_df: Results DataFrame
            output_dir: Output directory. If None, uses config default.
            
        Returns:
            Dictionary mapping format to saved file path
        """
        if results_df.empty:
            self.logger.warning("No results to save")
            return {}
        
        try:
            # Create output directory
            if output_dir is None:
                output_dir = self.config.output.output_dir
            
            output_path = create_output_directory(output_dir)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S") if self.config.output.include_timestamp else ""
            filename_template = self.config.output.filename_template
            
            filename = filename_template.format(
                n=self.config.analysis.top_n_stocks,
                timestamp=timestamp
            )
            
            base_path = output_path / filename
            
            # Save in multiple formats
            saved_files = save_dataframe_multiple_formats(
                results_df, str(base_path), self.config.output.save_formats
            )
            
            # Log saved files
            for fmt, path in saved_files.items():
                self.logger.info(f"Results saved ({fmt}): {path}")
            
            return saved_files
            
        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
            return {}
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of the current session."""
        duration = (datetime.now() - self.session_start).total_seconds()
        fetcher_stats = self.fetcher.get_session_stats()
        memory_usage = get_memory_usage()
        
        return {
            'session_duration': format_duration(duration),
            'session_stats': self.session_stats,
            'fetcher_stats': fetcher_stats,
            'memory_usage': memory_usage,
            'config_summary': {
                'api_rate_limit': self.config.api.rate_limit_delay,
                'max_retries': self.config.api.max_retries,
                'top_n_stocks': self.config.analysis.top_n_stocks,
                'min_trading_days': self.config.analysis.min_trading_days
            }
        }


def main():
    """Main execution function with CLI interface."""
    parser = argparse.ArgumentParser(
        description="Enhanced Stock Analysis System - Context7 Inspired",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python get_stock_list_enhanced.py --test
  python get_stock_list_enhanced.py --year 2024 --month 12
  python get_stock_list_enhanced.py --year 2024 --month 12 --max-stocks 50
  python get_stock_list_enhanced.py --config custom_config.json
        """
    )

    parser.add_argument('--test', action='store_true',
                       help='Run data retrieval test only')
    parser.add_argument('--year', type=int, default=2024,
                       help='Year for analysis (default: 2024)')
    parser.add_argument('--month', type=int, default=12,
                       help='Month for analysis (default: 12)')
    parser.add_argument('--max-stocks', type=int,
                       help='Maximum number of stocks to process')
    parser.add_argument('--config', type=str,
                       help='Path to configuration file')
    parser.add_argument('--output-dir', type=str,
                       help='Output directory for results')
    parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    parser.add_argument('--json-logs', action='store_true',
                       help='Enable JSON structured logging')

    args = parser.parse_args()

    try:
        # Load configuration
        config = None
        if args.config:
            config = StockConfig.load_from_file(args.config)

        # Override config with command line arguments
        if config is None:
            config = get_config()

        if args.log_level:
            config.logging.level = args.log_level
        if args.json_logs:
            config.logging.enable_json_logging = True

        # Initialize system
        system = EnhancedStockAnalysisSystem(config)

        # Run test if requested
        if args.test:
            system.logger.info("Running data retrieval test...")
            test_success = system.test_data_retrieval(year=args.year, month=args.month)

            if test_success:
                system.logger.info("✓ Test completed successfully")
                return 0
            else:
                system.logger.error("✗ Test failed")
                return 1

        # Run full analysis
        system.logger.info("Starting enhanced stock amplitude analysis...")

        results_df = system.get_top_amplitude_stocks(
            year=args.year,
            month=args.month,
            max_stocks=args.max_stocks
        )

        if results_df.empty:
            system.logger.error("No results generated")
            return 1

        # Display results
        print(f"\n{args.year}年{args.month}月日均振幅前{len(results_df)}的股票:")
        print("=" * 80)
        print(results_df.to_string(index=False))

        # Save results
        saved_files = system.save_results(results_df, args.output_dir)

        if saved_files:
            print(f"\n结果已保存:")
            for fmt, path in saved_files.items():
                print(f"  {fmt.upper()}: {path}")

        # Display session summary
        summary = system.get_session_summary()
        print(f"\n会话摘要:")
        print(f"  处理时间: {summary['session_duration']}")
        print(f"  处理股票: {summary['session_stats']['stocks_processed']}")
        print(f"  成功获取: {summary['session_stats']['stocks_successful']}")
        print(f"  分析结果: {summary['session_stats']['analysis_results']}")
        print(f"  成功率: {summary['fetcher_stats']['success_rate']:.1%}")

        system.logger.info("Analysis completed successfully")
        return 0

    except KeyboardInterrupt:
        print("\n分析被用户中断")
        return 1
    except Exception as e:
        logging.getLogger(__name__).error(f"Analysis failed: {e}", exc_info=True)
        return 1


# Backward compatibility functions
def test_stock_data_retrieval(stock_codes=None, year=2024, month=12):
    """Backward compatibility wrapper for the original test function."""
    system = EnhancedStockAnalysisSystem()
    return system.test_data_retrieval(stock_codes, year, month)


def get_top_10_amplitude_stocks(year=2024, month=12):
    """Backward compatibility wrapper for the original analysis function."""
    system = EnhancedStockAnalysisSystem()
    results_df = system.get_top_amplitude_stocks(year, month, max_stocks=100)
    return results_df


if __name__ == "__main__":
    sys.exit(main())
