"""
Test script for the enhanced stock analysis system.
"""

import sys
import logging
from datetime import datetime

# Import the enhanced system
from get_stock_list_enhanced import EnhancedStockAnalysisSystem
from config import StockConfig


def test_configuration():
    """Test configuration loading and validation."""
    print("Testing configuration system...")
    
    try:
        # Test default configuration
        config = StockConfig()
        config.validate()
        print("✓ Default configuration is valid")
        
        # Test configuration file loading
        config_from_file = StockConfig.load_from_file('stock_config.json')
        config_from_file.validate()
        print("✓ Configuration file loaded successfully")
        
        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_system_initialization():
    """Test system initialization."""
    print("\nTesting system initialization...")
    
    try:
        system = EnhancedStockAnalysisSystem()
        print("✓ System initialized successfully")
        
        # Test component initialization
        assert system.fetcher is not None, "Fetcher not initialized"
        assert system.analyzer is not None, "Analyzer not initialized"
        assert system.validator is not None, "Validator not initialized"
        print("✓ All components initialized")
        
        return True
    except Exception as e:
        print(f"✗ System initialization failed: {e}")
        return False


def test_data_retrieval():
    """Test data retrieval functionality."""
    print("\nTesting data retrieval...")
    
    try:
        system = EnhancedStockAnalysisSystem()
        
        # Test with a small subset of stocks
        test_stocks = ['000001', '000002']
        success = system.test_data_retrieval(stock_codes=test_stocks, year=2024, month=12)
        
        if success:
            print("✓ Data retrieval test passed")
            return True
        else:
            print("✗ Data retrieval test failed")
            return False
            
    except Exception as e:
        print(f"✗ Data retrieval test failed with exception: {e}")
        return False


def test_analysis_pipeline():
    """Test the full analysis pipeline with limited data."""
    print("\nTesting analysis pipeline...")
    
    try:
        # Create a custom configuration for testing
        config = StockConfig()
        config.api.max_stocks_per_batch = 10  # Limit for testing
        config.analysis.top_n_stocks = 5
        
        system = EnhancedStockAnalysisSystem(config)
        
        # Run analysis with limited stocks
        results_df = system.get_top_amplitude_stocks(
            year=2024, 
            month=12, 
            max_stocks=10  # Very limited for testing
        )
        
        if not results_df.empty:
            print(f"✓ Analysis completed successfully with {len(results_df)} results")
            print("Sample results:")
            print(results_df.head(3).to_string(index=False))
            return True
        else:
            print("✗ Analysis returned no results")
            return False
            
    except Exception as e:
        print(f"✗ Analysis pipeline test failed: {e}")
        return False


def test_session_stats():
    """Test session statistics functionality."""
    print("\nTesting session statistics...")
    
    try:
        system = EnhancedStockAnalysisSystem()
        
        # Get initial stats
        stats = system.get_session_summary()
        
        assert 'session_duration' in stats, "Missing session duration"
        assert 'session_stats' in stats, "Missing session stats"
        assert 'fetcher_stats' in stats, "Missing fetcher stats"
        
        print("✓ Session statistics working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Session statistics test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("Enhanced Stock Analysis System - Test Suite")
    print("=" * 50)
    
    # Set up logging for tests
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during testing
    
    tests = [
        test_configuration,
        test_system_initialization,
        test_session_stats,
        test_data_retrieval,
        # test_analysis_pipeline,  # Commented out as it requires API access
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} failed with exception: {e}")
    
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The enhanced system is ready to use.")
        return 0
    else:
        print("❌ Some tests failed. Please check the configuration and dependencies.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
