"""
Configuration management for the enhanced stock data retrieval system.
Inspired by Context7's configuration approach for better maintainability.
"""

import json
import os
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from pathlib import Path


@dataclass
class APIConfig:
    """API-related configuration settings."""
    rate_limit_delay: float = 0.05  # Delay between API calls in seconds
    max_retries: int = 3
    retry_backoff_factor: float = 2.0
    timeout: int = 30
    max_stocks_per_batch: int = 100


@dataclass
class AnalysisConfig:
    """Analysis-related configuration settings."""
    min_trading_days: int = 5
    amplitude_threshold: float = 0.0  # Minimum amplitude percentage
    top_n_stocks: int = 10
    required_columns: List[str] = field(default_factory=lambda: ['最高', '最低', '收盘'])


@dataclass
class LoggingConfig:
    """Logging configuration settings."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    enable_json_logging: bool = False
    log_file: Optional[str] = None
    max_log_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class OutputConfig:
    """Output configuration settings."""
    output_dir: str = "output"
    csv_encoding: str = "utf_8_sig"
    include_timestamp: bool = True
    save_formats: List[str] = field(default_factory=lambda: ["csv"])
    filename_template: str = "top_{n}_amplitude_stocks_{timestamp}"


@dataclass
class DataValidationConfig:
    """Data validation configuration settings."""
    enable_validation: bool = True
    max_price_change_percent: float = 50.0  # Maximum daily price change
    min_price: float = 0.01
    max_price: float = 10000.0
    enable_outlier_detection: bool = True
    outlier_threshold: float = 3.0  # Standard deviations


@dataclass
class StockConfig:
    """Main configuration class that combines all configuration sections."""
    api: APIConfig = field(default_factory=APIConfig)
    analysis: AnalysisConfig = field(default_factory=AnalysisConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    output: OutputConfig = field(default_factory=OutputConfig)
    validation: DataValidationConfig = field(default_factory=DataValidationConfig)
    
    # Test stock codes for validation
    test_stock_codes: List[str] = field(
        default_factory=lambda: ['000001', '000002', '600000', '600036', '000858']
    )
    
    @classmethod
    def load_from_file(cls, config_path: str) -> 'StockConfig':
        """
        Load configuration from a JSON file.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            StockConfig instance with loaded configuration
        """
        config_file = Path(config_path)
        if not config_file.exists():
            # Create default config file if it doesn't exist
            default_config = cls()
            default_config.save_to_file(config_path)
            return default_config
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            return cls._from_dict(config_data)
        except Exception as e:
            raise ValueError(f"Failed to load configuration from {config_path}: {e}")
    
    @classmethod
    def _from_dict(cls, data: Dict[str, Any]) -> 'StockConfig':
        """Create StockConfig from dictionary data."""
        config = cls()
        
        if 'api' in data:
            config.api = APIConfig(**data['api'])
        if 'analysis' in data:
            config.analysis = AnalysisConfig(**data['analysis'])
        if 'logging' in data:
            config.logging = LoggingConfig(**data['logging'])
        if 'output' in data:
            config.output = OutputConfig(**data['output'])
        if 'validation' in data:
            config.validation = DataValidationConfig(**data['validation'])
        if 'test_stock_codes' in data:
            config.test_stock_codes = data['test_stock_codes']
        
        return config
    
    def save_to_file(self, config_path: str) -> None:
        """
        Save configuration to a JSON file.
        
        Args:
            config_path: Path where to save the configuration file
        """
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        config_dict = {
            'api': self.api.__dict__,
            'analysis': self.analysis.__dict__,
            'logging': self.logging.__dict__,
            'output': self.output.__dict__,
            'validation': self.validation.__dict__,
            'test_stock_codes': self.test_stock_codes
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def update_from_env(self) -> None:
        """Update configuration from environment variables."""
        # API configuration
        if os.getenv('STOCK_API_RATE_LIMIT'):
            self.api.rate_limit_delay = float(os.getenv('STOCK_API_RATE_LIMIT'))
        if os.getenv('STOCK_API_MAX_RETRIES'):
            self.api.max_retries = int(os.getenv('STOCK_API_MAX_RETRIES'))
        
        # Analysis configuration
        if os.getenv('STOCK_MIN_TRADING_DAYS'):
            self.analysis.min_trading_days = int(os.getenv('STOCK_MIN_TRADING_DAYS'))
        if os.getenv('STOCK_TOP_N'):
            self.analysis.top_n_stocks = int(os.getenv('STOCK_TOP_N'))
        
        # Logging configuration
        if os.getenv('STOCK_LOG_LEVEL'):
            self.logging.level = os.getenv('STOCK_LOG_LEVEL')
        if os.getenv('STOCK_LOG_FILE'):
            self.logging.log_file = os.getenv('STOCK_LOG_FILE')
        
        # Output configuration
        if os.getenv('STOCK_OUTPUT_DIR'):
            self.output.output_dir = os.getenv('STOCK_OUTPUT_DIR')
    
    def validate(self) -> None:
        """Validate configuration values."""
        if self.api.rate_limit_delay < 0:
            raise ValueError("API rate limit delay must be non-negative")
        if self.api.max_retries < 0:
            raise ValueError("Max retries must be non-negative")
        if self.analysis.min_trading_days < 1:
            raise ValueError("Minimum trading days must be at least 1")
        if self.analysis.top_n_stocks < 1:
            raise ValueError("Top N stocks must be at least 1")
        if self.validation.min_price <= 0:
            raise ValueError("Minimum price must be positive")
        if self.validation.max_price <= self.validation.min_price:
            raise ValueError("Maximum price must be greater than minimum price")


# Global configuration instance
_config: Optional[StockConfig] = None


def get_config() -> StockConfig:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        config_path = os.getenv('STOCK_CONFIG_PATH', 'stock_config.json')
        _config = StockConfig.load_from_file(config_path)
        _config.update_from_env()
        _config.validate()
    return _config


def set_config(config: StockConfig) -> None:
    """Set the global configuration instance."""
    global _config
    config.validate()
    _config = config
