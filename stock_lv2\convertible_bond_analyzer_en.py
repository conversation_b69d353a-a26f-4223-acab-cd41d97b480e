"""
Convertible Bond Market Data Analysis System
Based on Context7 enhanced architecture pattern, providing convertible bond amplitude analysis functionality
"""

import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import time
import json
from pathlib import Path

# Import enhanced system core modules
from config import get_config, StockConfig
from stock_data_fetcher import RateLimiter, FetchResult
from data_validator import ValidationResult
from utils import (
    setup_logging, format_duration, format_number,
    validate_date_range, create_output_directory, 
    save_dataframe_multiple_formats, create_progress_bar,
    calculate_progress_eta
)


@dataclass
class ConvertibleBondConfig:
    """Convertible bond analysis specific configuration"""
    min_data_requirement: int = 1  # Since only current data is available, lower requirement
    amplitude_threshold: float = 0.0
    top_n_count: int = 10
    required_columns: List[str] = None
    
    def __post_init__(self):
        if self.required_columns is None:
            # Based on actual API returned column names
            self.required_columns = ['现价', '涨跌幅', '转股溢价率', '到期税前收益']


@dataclass
class ConvertibleBondAnalysisResult:
    """Convertible bond analysis result data structure"""
    bond_code: str
    bond_name: str
    daily_amplitude_pct: float
    trading_days: int
    average_price: float
    price_volatility: float
    total_return: float
    data_quality_score: float


class ConvertibleBondDataFetcher:
    """
    Convertible bond data fetcher with retry mechanism and error handling
    """
    
    def __init__(self, config: Optional[StockConfig] = None):
        """
        Initialize convertible bond data fetcher
        
        Args:
            config: System configuration, if None uses global config
        """
        self.config = config or get_config()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.rate_limiter = RateLimiter(self.config.api.rate_limit_delay)
        self.session_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_retries': 0,
            'start_time': datetime.now()
        }
    
    def get_convertible_bond_list(self) -> FetchResult:
        """
        Get list of all convertible bonds with retry logic
        
        Returns:
            FetchResult containing bond list or error information
        """
        start_time = time.time()
        
        for attempt in range(1, self.config.api.max_retries + 1):
            try:
                self.rate_limiter.wait()
                self.session_stats['total_requests'] += 1
                
                self.logger.debug(f"Fetching convertible bond list (attempt {attempt})")
                
                # Use akshare to get convertible bond data
                bond_list = ak.bond_cb_jsl()
                
                if bond_list.empty:
                    raise ValueError("Received empty convertible bond list")
                
                self.session_stats['successful_requests'] += 1
                duration = time.time() - start_time
                
                self.logger.info(f"Successfully fetched {len(bond_list)} convertible bonds")
                return FetchResult(
                    success=True,
                    data=bond_list,
                    attempts=attempt,
                    duration=duration
                )
                
            except Exception as e:
                self.session_stats['failed_requests'] += 1
                error_msg = f"Attempt {attempt} failed: {str(e)}"
                self.logger.warning(error_msg)
                
                if attempt < self.config.api.max_retries:
                    self.session_stats['total_retries'] += 1
                    wait_time = self._calculate_backoff_delay(attempt)
                    self.logger.info(f"Retrying in {wait_time:.2f} seconds...")
                    time.sleep(wait_time)
                else:
                    duration = time.time() - start_time
                    final_error = f"Failed to fetch convertible bond list after {attempt} attempts. Last error: {str(e)}"
                    self.logger.error(final_error)
                    return FetchResult(
                        success=False,
                        error=final_error,
                        attempts=attempt,
                        duration=duration
                    )
        
        return FetchResult(success=False, error="Unexpected error in retry loop")
    
    def get_convertible_bond_current_data(self) -> FetchResult:
        """
        Get convertible bond current market data (including price information)
        
        Returns:
            FetchResult containing convertible bond market data or error information
        """
        start_time = time.time()
        
        for attempt in range(1, self.config.api.max_retries + 1):
            try:
                self.rate_limiter.wait()
                self.session_stats['total_requests'] += 1
                
                self.logger.debug(f"Fetching convertible bond market data (attempt {attempt})")
                
                # Get convertible bond market data, this API includes price information
                market_data = ak.bond_cb_jsl()
                
                if market_data.empty:
                    raise ValueError("Received empty convertible bond market data")
                
                self.session_stats['successful_requests'] += 1
                duration = time.time() - start_time
                
                self.logger.debug(f"Successfully fetched convertible bond market data with {len(market_data)} bonds")
                return FetchResult(
                    success=True,
                    data=market_data,
                    attempts=attempt,
                    duration=duration
                )
                
            except Exception as e:
                self.session_stats['failed_requests'] += 1
                error_msg = f"Attempt {attempt} failed: {str(e)}"
                self.logger.debug(error_msg)
                
                if attempt < self.config.api.max_retries:
                    self.session_stats['total_retries'] += 1
                    wait_time = self._calculate_backoff_delay(attempt)
                    time.sleep(wait_time)
                else:
                    duration = time.time() - start_time
                    final_error = f"Failed to fetch convertible bond market data after {attempt} attempts. Last error: {str(e)}"
                    return FetchResult(
                        success=False,
                        error=final_error,
                        attempts=attempt,
                        duration=duration
                    )
        
        return FetchResult(success=False, error="Unexpected error in retry loop")
    
    def _calculate_backoff_delay(self, attempt: int) -> float:
        """
        Calculate exponential backoff delay with jitter
        
        Args:
            attempt: Current attempt number (1-based)
            
        Returns:
            Delay in seconds
        """
        import random
        base_delay = self.config.api.rate_limit_delay * (self.config.api.retry_backoff_factor ** (attempt - 1))
        # Add jitter to avoid thundering herd
        jitter = random.uniform(0.5, 1.5)
        return base_delay * jitter
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get statistics for the current session"""
        duration = (datetime.now() - self.session_stats['start_time']).total_seconds()
        success_rate = (
            self.session_stats['successful_requests'] / self.session_stats['total_requests']
            if self.session_stats['total_requests'] > 0 else 0
        )
        
        return {
            **self.session_stats,
            'session_duration': duration,
            'success_rate': success_rate,
            'requests_per_second': self.session_stats['total_requests'] / duration if duration > 0 else 0
        }


class ConvertibleBondDataValidator:
    """
    Convertible bond data validator providing data quality checks and cleaning functionality
    """
    
    def __init__(self, config: ConvertibleBondConfig):
        """
        Initialize validator
        
        Args:
            config: Convertible bond analysis configuration
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def validate_convertible_bond_data(self, data: pd.DataFrame, bond_code: str) -> ValidationResult:
        """
        Validate single convertible bond data
        
        Args:
            data: Convertible bond data DataFrame
            bond_code: Convertible bond code
            
        Returns:
            ValidationResult: Validation result
        """
        errors = []
        warnings = []
        
        if data.empty:
            errors.append(f"Convertible bond {bond_code}: No data available")
            return ValidationResult(is_valid=False, errors=errors, warnings=warnings)
        
        # Check required columns
        missing_cols = [col for col in self.config.required_columns if col not in data.columns]
        if missing_cols:
            errors.append(f"Convertible bond {bond_code}: Missing required columns: {missing_cols}")
            return ValidationResult(is_valid=False, errors=errors, warnings=warnings)
        
        cleaned_data = data.copy()
        
        # Data type validation and cleaning
        numeric_columns = ['现价', '涨跌幅', '转股溢价率', '到期税前收益']
        for col_name in numeric_columns:
            if col_name in cleaned_data.columns:
                # Convert to numeric type
                non_numeric = pd.to_numeric(cleaned_data[col_name], errors='coerce').isna()
                if non_numeric.any():
                    warnings.append(f"Convertible bond {bond_code}: Non-numeric data in {col_name} column")
                    cleaned_data[col_name] = pd.to_numeric(cleaned_data[col_name], errors='coerce')
                
                # Price and ratio range validation
                if col_name == '现价':
                    invalid_prices = (cleaned_data[col_name] <= 0) | (cleaned_data[col_name] > 1000)
                    if invalid_prices.any():
                        warnings.append(f"Convertible bond {bond_code}: {col_name} column has {invalid_prices.sum()} invalid prices")
                        cleaned_data = cleaned_data[~invalid_prices]
                elif col_name == '涨跌幅':
                    # Price change usually between -50% to 50%
                    invalid_changes = (cleaned_data[col_name] < -50) | (cleaned_data[col_name] > 50)
                    if invalid_changes.any():
                        warnings.append(f"Convertible bond {bond_code}: {col_name} column has {invalid_changes.sum()} outliers")
        
        # Remove rows with missing critical data
        critical_cols = ['现价']
        before_count = len(cleaned_data)
        cleaned_data = cleaned_data.dropna(subset=critical_cols)
        after_count = len(cleaned_data)
        
        if before_count != after_count:
            warnings.append(f"Convertible bond {bond_code}: Removed {before_count - after_count} rows with missing critical data")
        
        # Final validation - for current data, just need to have data
        is_valid = len(errors) == 0 and len(cleaned_data) >= self.config.min_data_requirement
        
        if not is_valid and len(errors) == 0:
            errors.append(
                f"Convertible bond {bond_code}: Insufficient data after cleaning "
                f"({len(cleaned_data)} < {self.config.min_data_requirement} required rows)"
            )
        
        # Calculate data quality score
        quality_score = self._calculate_data_quality_score(cleaned_data) if is_valid else 0.0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            cleaned_data=cleaned_data if is_valid else None,
            stats={'data_quality_score': quality_score, 'cleaned_rows': len(cleaned_data)}
        )
    
    def _calculate_data_quality_score(self, data: pd.DataFrame) -> float:
        """
        Calculate data quality score
        
        Args:
            data: Cleaned data
            
        Returns:
            Quality score (between 0-1)
        """
        try:
            if data.empty:
                return 0.0
            
            score = 1.0
            
            # Check missing value ratio
            missing_ratio = data.isnull().sum().sum() / (len(data) * len(data.columns))
            score -= missing_ratio * 0.3
            
            # Check for extreme values (more than 50% daily change)
            if '涨跌幅' in data.columns and len(data) > 0:
                extreme_changes = (data['涨跌幅'].abs() > 50).sum()
                score -= (extreme_changes / len(data)) * 0.2
            
            return max(0.0, score)
            
        except Exception:
            return 0.5  # Default score if calculation fails


class ConvertibleBondAnalyzer:
    """
    Convertible bond analyzer for calculating amplitude and other technical indicators
    """

    def __init__(self, config: ConvertibleBondConfig):
        """
        Initialize analyzer

        Args:
            config: Convertible bond analysis configuration
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def calculate_daily_performance_indicator(self, data: pd.DataFrame, bond_code: str) -> Optional[float]:
        """
        Calculate convertible bond comprehensive performance indicator based on current market data
        Since only current data is available, we use absolute value of price change and conversion premium as comprehensive indicator

        Args:
            data: Convertible bond current market data DataFrame
            bond_code: Convertible bond code

        Returns:
            Comprehensive performance indicator, or None if calculation fails
        """
        try:
            if data.empty:
                self.logger.warning(f"Convertible bond {bond_code}: Empty data, cannot calculate performance indicator")
                return None

            # Use absolute value of price change as activity indicator
            if '涨跌幅' in data.columns:
                price_change = data['涨跌幅'].iloc[0] if len(data) > 0 else 0
                activity_indicator = abs(price_change)

                self.logger.debug(f"Convertible bond {bond_code}: Daily price change {price_change:.2f}%, activity indicator {activity_indicator:.2f}%")
                return activity_indicator
            else:
                self.logger.warning(f"Convertible bond {bond_code}: Missing price change data")
                return None

        except Exception as e:
            self.logger.error(f"Convertible bond {bond_code}: Performance indicator calculation error: {e}")
            return None

    def calculate_conversion_premium_rate(self, data: pd.DataFrame, bond_code: str) -> Optional[float]:
        """
        Get conversion premium rate

        Args:
            data: Convertible bond data DataFrame
            bond_code: Convertible bond code

        Returns:
            Conversion premium rate, or None if calculation fails
        """
        try:
            if '转股溢价率' in data.columns and len(data) > 0:
                premium_rate = data['转股溢价率'].iloc[0]
                return float(premium_rate) if pd.notna(premium_rate) else None
            return None

        except Exception as e:
            self.logger.error(f"Convertible bond {bond_code}: Conversion premium rate calculation error: {e}")
            return None

    def calculate_market_statistics(self, data: pd.DataFrame, bond_code: str) -> Dict[str, float]:
        """
        Calculate convertible bond market statistics

        Args:
            data: Convertible bond data DataFrame
            bond_code: Convertible bond code

        Returns:
            Market statistics dictionary
        """
        try:
            stats = {}

            if len(data) > 0:
                row_data = data.iloc[0]

                # Basic price information
                if '现价' in data.columns:
                    stats['current_price'] = float(row_data['现价']) if pd.notna(row_data['现价']) else 0.0

                # Price change
                if '涨跌幅' in data.columns:
                    stats['price_change'] = float(row_data['涨跌幅']) if pd.notna(row_data['涨跌幅']) else 0.0

                # Conversion premium rate
                if '转股溢价率' in data.columns:
                    stats['conversion_premium'] = float(row_data['转股溢价率']) if pd.notna(row_data['转股溢价率']) else 0.0

                # Yield to maturity before tax
                if '到期税前收益' in data.columns:
                    stats['ytm_before_tax'] = float(row_data['到期税前收益']) if pd.notna(row_data['到期税前收益']) else 0.0

                # Trading volume
                if '成交额' in data.columns:
                    stats['trading_volume'] = float(row_data['成交额']) if pd.notna(row_data['成交额']) else 0.0

                # Turnover rate
                if '换手率' in data.columns:
                    stats['turnover_rate'] = float(row_data['换手率']) if pd.notna(row_data['换手率']) else 0.0

            return stats

        except Exception as e:
            self.logger.error(f"Convertible bond {bond_code}: Market statistics calculation error: {e}")
            return {}

    def analyze_convertible_bond(self, data: pd.DataFrame, bond_code: str, bond_name: str,
                               data_quality_score: float = 0.0) -> Optional[ConvertibleBondAnalysisResult]:
        """
        Perform comprehensive analysis of a single convertible bond

        Args:
            data: Convertible bond data DataFrame
            bond_code: Convertible bond code
            bond_name: Convertible bond name
            data_quality_score: Data quality score

        Returns:
            ConvertibleBondAnalysisResult with all calculated metrics, or None if analysis fails
        """
        try:
            # Calculate daily performance indicator (using absolute value of price change)
            performance_indicator = self.calculate_daily_performance_indicator(data, bond_code)
            if performance_indicator is None:
                self.logger.warning(f"Convertible bond {bond_code}: Performance indicator calculation failed")
                return None

            # Check performance indicator threshold
            if performance_indicator < self.config.amplitude_threshold:
                self.logger.debug(
                    f"Convertible bond {bond_code}: Performance indicator below threshold "
                    f"({performance_indicator:.2f}% < {self.config.amplitude_threshold}%)"
                )
                return None

            # Calculate other indicators
            conversion_premium = self.calculate_conversion_premium_rate(data, bond_code) or 0.0
            market_stats = self.calculate_market_statistics(data, bond_code)

            return ConvertibleBondAnalysisResult(
                bond_code=bond_code,
                bond_name=bond_name,
                daily_amplitude_pct=performance_indicator,  # Use performance indicator as "amplitude"
                trading_days=1,  # Current data only has 1 day
                average_price=market_stats.get('current_price', 0.0),
                price_volatility=conversion_premium,  # Use conversion premium as "volatility"
                total_return=market_stats.get('price_change', 0.0),  # Use price change as "return"
                data_quality_score=data_quality_score
            )

        except Exception as e:
            self.logger.error(f"Convertible bond {bond_code}: Analysis failed: {e}")
            return None

    def batch_analyze(self, bond_market_data: pd.DataFrame) -> List[ConvertibleBondAnalysisResult]:
        """
        Batch analyze convertible bond market data and return sorted results

        Args:
            bond_market_data: DataFrame containing all convertible bond current market data

        Returns:
            List of analysis results sorted by performance indicator
        """
        results = []

        for index, row_data in bond_market_data.iterrows():
            try:
                bond_code = row_data['代码']
                bond_name = row_data['转债名称']

                # Convert single row data to DataFrame for analysis
                single_row_data = pd.DataFrame([row_data])

                # Validate data
                validation_result = self.data_validator.validate_convertible_bond_data(single_row_data, bond_code)

                if validation_result.is_valid:
                    data_quality_score = validation_result.stats.get('data_quality_score', 0.0) if validation_result.stats else 0.0

                    result = self.analyze_convertible_bond(validation_result.cleaned_data, bond_code, bond_name, data_quality_score)
                    if result:
                        results.append(result)
                else:
                    self.logger.debug(f"Convertible bond {bond_code} validation failed: {validation_result.errors}")

            except Exception as e:
                self.logger.debug(f"Error processing convertible bond data: {e}")
                continue

        # Sort by performance indicator descending and take top N
        results.sort(key=lambda x: x.daily_amplitude_pct, reverse=True)
        top_results = results[:self.config.top_n_count]

        self.logger.info(
            f"Analysis completed: Selected top {len(top_results)} from {len(results)} valid convertible bonds"
        )

        return top_results


class ConvertibleBondAnalysisSystem:
    """
    Main convertible bond analysis system class, coordinating data fetching, validation, analysis and output
    """

    def __init__(self, config: Optional[StockConfig] = None):
        """
        Initialize convertible bond analysis system

        Args:
            config: System configuration, if None uses global config
        """
        self.system_config = config or get_config()
        self.bond_config = ConvertibleBondConfig()

        # Setup logging
        self.logger = setup_logging(self.system_config.logging)
        self.logger.info("Convertible Bond Analysis System initialized")

        # Initialize components
        self.data_fetcher = ConvertibleBondDataFetcher(self.system_config)
        self.data_validator = ConvertibleBondDataValidator(self.bond_config)
        self.analyzer = ConvertibleBondAnalyzer(self.bond_config)
        # Pass validator to analyzer
        self.analyzer.data_validator = self.data_validator

        # Session tracking
        self.session_start = datetime.now()
        self.session_stats = {
            'bonds_processed': 0,
            'bonds_successful': 0,
            'validation_errors': 0,
            'analysis_results': 0
        }

    def test_data_retrieval(self, year: int = 2025, month: int = 6) -> bool:
        """
        Test convertible bond data retrieval functionality

        Args:
            year: Test year
            month: Test month

        Returns:
            Whether test was successful
        """
        self.logger.info("Starting convertible bond data retrieval test...")

        try:
            # Get convertible bond current market data
            market_data_result = self.data_fetcher.get_convertible_bond_current_data()

            if not market_data_result.success:
                self.logger.error(f"Failed to get convertible bond market data: {market_data_result.error}")
                return False

            bond_market_data = market_data_result.data
            self.logger.info(f"Successfully retrieved market data for {len(bond_market_data)} convertible bonds")

            # Test validation of first few convertible bonds
            test_count = min(3, len(bond_market_data))
            success_count = 0

            for i in range(test_count):
                try:
                    row_data = bond_market_data.iloc[i]
                    bond_code = row_data['代码']
                    bond_name = row_data['转债名称']

                    self.logger.info(f"Testing convertible bond {bond_code} ({bond_name}) ({i+1}/{test_count})")

                    # Validate data
                    single_row_data = pd.DataFrame([row_data])
                    validation_result = self.data_validator.validate_convertible_bond_data(single_row_data, bond_code)

                    if validation_result.is_valid:
                        self.logger.info(f"✓ Convertible bond {bond_code}: Data validation passed")
                        success_count += 1
                    else:
                        self.logger.warning(f"✗ Convertible bond {bond_code}: Data validation failed - {validation_result.errors}")

                except Exception as e:
                    self.logger.warning(f"✗ Convertible bond test failed: {e}")

            success_rate = success_count / test_count * 100 if test_count > 0 else 0
            self.logger.info(f"Test completed: {success_count}/{test_count} successful ({success_rate:.1f}%)")

            return success_rate >= 50  # Consider test successful if at least 50% pass

        except Exception as e:
            self.logger.error(f"Test failed: {e}")
            return False

    def get_top_amplitude_convertible_bonds(self, year: int = 2025, month: int = 6,
                                          max_bonds: Optional[int] = None) -> pd.DataFrame:
        """
        Get top amplitude convertible bonds for the specified period

        Args:
            year: Analysis year
            month: Analysis month
            max_bonds: Maximum number of bonds to process (None for all)

        Returns:
            DataFrame with top amplitude convertible bonds
        """
        self.logger.info(f"Starting convertible bond amplitude analysis for {year}-{month:02d}")

        try:
            # Get convertible bond current market data
            self.logger.info("Fetching convertible bond market data...")
            market_data_result = self.data_fetcher.get_convertible_bond_current_data()

            if not market_data_result.success:
                self.logger.error(f"Failed to get convertible bond market data: {market_data_result.error}")
                return pd.DataFrame()

            bond_market_data = market_data_result.data

            # Limit processing count
            if max_bonds:
                bond_market_data = bond_market_data.head(max_bonds)

            self.logger.info(f"Will analyze {len(bond_market_data)} convertible bonds")

            # Analyze convertible bonds
            self.logger.info("Analyzing convertible bond performance indicators...")
            analysis_results = self.analyzer.batch_analyze(bond_market_data)

            if not analysis_results:
                self.logger.warning("No analysis results generated")
                return pd.DataFrame()

            # Convert to DataFrame
            results_data = []
            for result in analysis_results:
                results_data.append({
                    'Bond Code': result.bond_code,
                    'Bond Name': result.bond_name,
                    'Daily Amplitude (%)': round(result.daily_amplitude_pct, 2),
                    'Trading Days': result.trading_days,
                    'Average Price': round(result.average_price, 2),
                    'Price Volatility (%)': round(result.price_volatility, 2),
                    'Total Return (%)': round(result.total_return, 2),
                    'Data Quality Score': round(result.data_quality_score, 2)
                })

            result_df = pd.DataFrame(results_data)

            # Update session stats
            self.session_stats.update({
                'bonds_successful': len(bond_market_data),
                'validation_errors': len(bond_market_data) - len(analysis_results),
                'analysis_results': len(analysis_results)
            })

            return result_df

        except Exception as e:
            self.logger.error(f"Analysis failed: {e}", exc_info=True)
            return pd.DataFrame()

    def save_results(self, results_df: pd.DataFrame, output_dir: Optional[str] = None) -> Dict[str, str]:
        """
        Save analysis results in multiple formats

        Args:
            results_df: Results DataFrame
            output_dir: Output directory, if None uses config default

        Returns:
            Dictionary mapping format to saved file path
        """
        if results_df.empty:
            self.logger.warning("No results to save")
            return {}

        try:
            # Create output directory
            if output_dir is None:
                output_dir = self.system_config.output.output_dir

            output_path = create_output_directory(output_dir)

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S") if self.system_config.output.include_timestamp else ""
            filename_template = "convertible_bond_amplitude_analysis_top{n}_{timestamp}"

            filename = filename_template.format(
                n=self.bond_config.top_n_count,
                timestamp=timestamp
            )

            base_path = output_path / filename

            # Save in multiple formats
            saved_files = save_dataframe_multiple_formats(
                results_df, str(base_path), self.system_config.output.save_formats
            )

            # Log saved files
            for fmt, path in saved_files.items():
                self.logger.info(f"Results saved ({fmt}): {path}")

            return saved_files

        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
            return {}

    def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of the current session"""
        duration = (datetime.now() - self.session_start).total_seconds()
        fetcher_stats = self.data_fetcher.get_session_stats()

        return {
            'session_duration': format_duration(duration),
            'session_stats': self.session_stats,
            'fetcher_stats': fetcher_stats,
            'config_summary': {
                'api_rate_limit': self.system_config.api.rate_limit_delay,
                'max_retries': self.system_config.api.max_retries,
                'top_n_count': self.bond_config.top_n_count,
                'min_data_requirement': self.bond_config.min_data_requirement
            }
        }


def main():
    """Main execution function with CLI interface"""
    import argparse
    import sys

    parser = argparse.ArgumentParser(
        description="Convertible Bond Market Data Analysis System - Context7 Enhanced Architecture",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python convertible_bond_analyzer_en.py --test
  python convertible_bond_analyzer_en.py --year 2025 --month 6
  python convertible_bond_analyzer_en.py --year 2024 --month 12 --max-bonds 30
  python convertible_bond_analyzer_en.py --config custom_config.json
        """
    )

    parser.add_argument('--test', action='store_true',
                       help='Run data retrieval test only')
    parser.add_argument('--year', type=int, default=2025,
                       help='Analysis year (default: 2025)')
    parser.add_argument('--month', type=int, default=6,
                       help='Analysis month (default: 6)')
    parser.add_argument('--max-bonds', type=int,
                       help='Maximum number of bonds to process')
    parser.add_argument('--config', type=str,
                       help='Configuration file path')
    parser.add_argument('--output-dir', type=str,
                       help='Output directory for results')
    parser.add_argument('--log-level', type=str,
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    parser.add_argument('--json-logs', action='store_true',
                       help='Enable JSON structured logging')

    args = parser.parse_args()

    try:
        # Load configuration
        config = None
        if args.config:
            from config import StockConfig
            config = StockConfig.load_from_file(args.config)

        # Override config with command line arguments
        if config is None:
            config = get_config()

        if args.log_level:
            config.logging.level = args.log_level
        if args.json_logs:
            config.logging.enable_json_logging = True

        # Initialize system
        system = ConvertibleBondAnalysisSystem(config)

        # Run test if requested
        if args.test:
            system.logger.info("Running convertible bond data retrieval test...")
            test_success = system.test_data_retrieval(year=args.year, month=args.month)

            if test_success:
                system.logger.info("✓ Test completed successfully")
                return 0
            else:
                system.logger.error("✗ Test failed")
                return 1

        # Run full analysis
        system.logger.info("Starting convertible bond amplitude analysis...")

        results_df = system.get_top_amplitude_convertible_bonds(
            year=args.year,
            month=args.month,
            max_bonds=args.max_bonds
        )

        if results_df.empty:
            system.logger.error("No results generated")
            return 1

        # Display results
        print(f"\nTop {len(results_df)} Convertible Bonds by Daily Amplitude for {args.year}-{args.month:02d}:")
        print("=" * 120)
        print(results_df.to_string(index=False))

        # Save results
        saved_files = system.save_results(results_df, args.output_dir)

        if saved_files:
            print(f"\nResults saved:")
            for fmt, path in saved_files.items():
                print(f"  {fmt.upper()}: {path}")

        # Display session summary
        summary = system.get_session_summary()
        print(f"\nSession Summary:")
        print(f"  Processing Time: {summary['session_duration']}")
        print(f"  Bonds Processed: {summary['session_stats']['bonds_processed']}")
        print(f"  Successful Bonds: {summary['session_stats']['bonds_successful']}")
        print(f"  Analysis Results: {summary['session_stats']['analysis_results']}")
        if summary['fetcher_stats']['total_requests'] > 0:
            print(f"  Success Rate: {summary['fetcher_stats']['success_rate']:.1%}")

        system.logger.info("Convertible bond analysis completed successfully")
        return 0

    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user")
        return 1
    except Exception as e:
        logging.getLogger(__name__).error(f"Analysis failed: {e}", exc_info=True)
        return 1


# Backward compatibility functions
def test_convertible_bond_data_retrieval(year=2025, month=6):
    """Backward compatibility test function"""
    system = ConvertibleBondAnalysisSystem()
    return system.test_data_retrieval(year, month)


def get_top_10_amplitude_convertible_bonds(year=2025, month=6):
    """Backward compatibility analysis function"""
    system = ConvertibleBondAnalysisSystem()
    results_df = system.get_top_amplitude_convertible_bonds(year, month, max_bonds=50)
    return results_df


if __name__ == "__main__":
    import sys
    sys.exit(main())
