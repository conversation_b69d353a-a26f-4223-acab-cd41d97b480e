"""
Utility functions and enhanced logging setup.
Inspired by Context7's comprehensive utility approach.
"""

import logging
import logging.handlers
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, Optional, List
import pandas as pd

from config import get_config, LoggingConfig


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging(config: Optional[LoggingConfig] = None) -> logging.Logger:
    """
    Set up enhanced logging with optional JSON formatting and file output.
    
    Args:
        config: Logging configuration. If None, uses global config.
        
    Returns:
        Configured logger instance
    """
    if config is None:
        config = get_config().logging
    
    # Create root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, config.level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    
    if config.enable_json_logging:
        console_handler.setFormatter(JSONFormatter())
    else:
        console_handler.setFormatter(logging.Formatter(config.format))
    
    logger.addHandler(console_handler)
    
    # File handler if specified
    if config.log_file:
        log_path = Path(config.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            config.log_file,
            maxBytes=config.max_log_size,
            backupCount=config.backup_count,
            encoding='utf-8'
        )
        
        if config.enable_json_logging:
            file_handler.setFormatter(JSONFormatter())
        else:
            file_handler.setFormatter(logging.Formatter(config.format))
        
        logger.addHandler(file_handler)
    
    return logger


def log_with_context(logger: logging.Logger, level: str, message: str, **context):
    """
    Log a message with additional context fields.
    
    Args:
        logger: Logger instance
        level: Log level (info, warning, error, etc.)
        message: Log message
        **context: Additional context fields
    """
    # Create a log record with extra fields
    log_method = getattr(logger, level.lower())
    
    # Create a custom record with extra fields
    record = logger.makeRecord(
        logger.name, getattr(logging, level.upper()), 
        '', 0, message, (), None
    )
    record.extra_fields = context
    
    logger.handle(record)


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.2f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def format_number(number: float, precision: int = 2) -> str:
    """
    Format number with appropriate precision and thousand separators.
    
    Args:
        number: Number to format
        precision: Decimal precision
        
    Returns:
        Formatted number string
    """
    if abs(number) >= 1_000_000:
        return f"{number/1_000_000:.{precision}f}M"
    elif abs(number) >= 1_000:
        return f"{number/1_000:.{precision}f}K"
    else:
        return f"{number:.{precision}f}"


def validate_date_range(start_date: str, end_date: str) -> tuple[str, str]:
    """
    Validate and adjust date range to ensure it's reasonable.
    
    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        
    Returns:
        Tuple of validated (start_date, end_date)
        
    Raises:
        ValueError: If date range is invalid
    """
    try:
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        
        if start > end:
            raise ValueError("Start date must be before end date")
        
        # Check if dates are too far in the future
        today = datetime.now()
        if start > today:
            raise ValueError("Start date cannot be in the future")
        
        # Adjust end date if it's in the future
        if end > today:
            end = today
            end_date = end.strftime("%Y-%m-%d")
        
        # Check if date range is too large (more than 5 years)
        if (end - start).days > 5 * 365:
            raise ValueError("Date range cannot exceed 5 years")
        
        return start_date, end_date
        
    except ValueError as e:
        if "time data" in str(e):
            raise ValueError("Invalid date format. Use YYYY-MM-DD format")
        raise


def create_output_directory(base_dir: str) -> Path:
    """
    Create output directory with timestamp.
    
    Args:
        base_dir: Base directory path
        
    Returns:
        Path to created directory
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path(base_dir) / f"analysis_{timestamp}"
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir


def save_dataframe_multiple_formats(df: pd.DataFrame, base_path: str, 
                                   formats: List[str] = None) -> Dict[str, str]:
    """
    Save DataFrame in multiple formats.
    
    Args:
        df: DataFrame to save
        base_path: Base file path without extension
        formats: List of formats to save (csv, excel, json)
        
    Returns:
        Dictionary mapping format to saved file path
    """
    if formats is None:
        formats = ['csv']
    
    saved_files = {}
    base_path = Path(base_path)
    
    for fmt in formats:
        try:
            if fmt.lower() == 'csv':
                file_path = base_path.with_suffix('.csv')
                df.to_csv(file_path, index=False, encoding='utf_8_sig')
                saved_files['csv'] = str(file_path)
                
            elif fmt.lower() == 'excel':
                file_path = base_path.with_suffix('.xlsx')
                df.to_excel(file_path, index=False, engine='openpyxl')
                saved_files['excel'] = str(file_path)
                
            elif fmt.lower() == 'json':
                file_path = base_path.with_suffix('.json')
                df.to_json(file_path, orient='records', force_ascii=False, indent=2)
                saved_files['json'] = str(file_path)
                
        except Exception as e:
            logging.getLogger(__name__).error(f"Failed to save {fmt} format: {e}")
    
    return saved_files


def calculate_progress_eta(start_time: datetime, completed: int, total: int) -> Optional[str]:
    """
    Calculate estimated time of arrival for a process.
    
    Args:
        start_time: Process start time
        completed: Number of completed items
        total: Total number of items
        
    Returns:
        ETA string or None if cannot calculate
    """
    if completed == 0 or total == 0:
        return None
    
    elapsed = (datetime.now() - start_time).total_seconds()
    rate = completed / elapsed
    remaining = total - completed
    
    if rate > 0:
        eta_seconds = remaining / rate
        eta_time = datetime.now() + timedelta(seconds=eta_seconds)
        return eta_time.strftime("%H:%M:%S")
    
    return None


def create_progress_bar(completed: int, total: int, width: int = 50) -> str:
    """
    Create a text-based progress bar.
    
    Args:
        completed: Number of completed items
        total: Total number of items
        width: Width of progress bar in characters
        
    Returns:
        Progress bar string
    """
    if total == 0:
        return "[" + "=" * width + "] 100%"
    
    progress = completed / total
    filled = int(width * progress)
    bar = "=" * filled + "-" * (width - filled)
    percentage = progress * 100
    
    return f"[{bar}] {percentage:.1f}%"


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename by removing invalid characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove multiple consecutive underscores
    while '__' in filename:
        filename = filename.replace('__', '_')
    
    return filename.strip('_')


def get_memory_usage() -> Dict[str, float]:
    """
    Get current memory usage information.
    
    Returns:
        Dictionary with memory usage in MB
    """
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
            'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            'percent': process.memory_percent()
        }
    except ImportError:
        return {'error': 'psutil not available'}
    except Exception as e:
        return {'error': str(e)}


def retry_on_exception(max_retries: int = 3, delay: float = 1.0,
                      backoff_factor: float = 2.0):
    """
    Decorator for retrying functions on exception.

    Args:
        max_retries: Maximum number of retries
        delay: Initial delay between retries
        backoff_factor: Multiplier for delay on each retry
    """
    import time

    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        raise last_exception

            return None
        return wrapper
    return decorator
