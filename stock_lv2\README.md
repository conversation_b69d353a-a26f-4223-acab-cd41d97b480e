# Enhanced Stock Analysis System

Enhanced stock data retrieval system with robust error handling, configuration management, data validation, and modular design.

## Features

- **Robust Error Handling**: Exponential backoff retry logic with circuit breaker patterns
- **Configuration Management**: JSON-based configuration with environment variable overrides
- **Data Validation**: Comprehensive data quality checks and cleaning
- **Modular Architecture**: Clean separation of concerns with dedicated modules
- **Enhanced Logging**: Structured JSON logging with performance metrics
- **Multiple Output Formats**: CSV, Excel, and JSON export capabilities
- **Amplitude Analysis**: Calculate daily amplitude with statistical validation
- **Data Quality Scoring**: Automated data quality assessment
- **Outlier Detection**: Statistical outlier identification and handling
- **Progress Tracking**: Real-time progress reporting with ETA calculation
- **Session Statistics**: Comprehensive session performance metrics
- **Type Safety**: Full type hints throughout the codebase
- **Parallel Processing**: Concurrent data fetching with rate limiting
- **Memory Monitoring**: Built-in memory usage tracking
- **Backward Compatibility**: Compatible with original script interface

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. (Optional) Install development dependencies:
```bash
pip install -r requirements.txt pytest black flake8 mypy
```

## Quick Start

### Basic Usage
```bash
# Run with default settings (2024-12)
python get_stock_list_enhanced.py

# Analyze specific period
python get_stock_list_enhanced.py --year 2024 --month 11

# Limit number of stocks for testing
python get_stock_list_enhanced.py --max-stocks 50
```

### Test Data Retrieval
```bash
# Test system functionality
python get_stock_list_enhanced.py --test

# Test with specific period
python get_stock_list_enhanced.py --test --year 2024 --month 12
```

### Advanced Usage
```bash
# Use custom configuration
python get_stock_list_enhanced.py --config custom_config.json

# Enable debug logging with JSON format
python get_stock_list_enhanced.py --log-level DEBUG --json-logs

# Specify output directory
python get_stock_list_enhanced.py --output-dir /path/to/output
```

## Configuration

The system uses a JSON configuration file (`stock_config.json`) with the following sections:

### API Configuration
```json
{
  "api": {
    "rate_limit_delay": 0.05,      // Delay between API calls (seconds)
    "max_retries": 3,              // Maximum retry attempts
    "retry_backoff_factor": 2.0,   // Exponential backoff multiplier
    "timeout": 30,                 // Request timeout (seconds)
    "max_stocks_per_batch": 100    // Batch size for processing
  }
}
```

### Analysis Configuration
```json
{
  "analysis": {
    "min_trading_days": 5,         // Minimum trading days required
    "amplitude_threshold": 0.0,    // Minimum amplitude threshold (%)
    "top_n_stocks": 10,           // Number of top stocks to return
    "required_columns": ["最高", "最低", "收盘"]
  }
}
```

### Validation Configuration
```json
{
  "validation": {
    "enable_validation": true,           // Enable data validation
    "max_price_change_percent": 50.0,   // Maximum daily price change (%)
    "min_price": 0.01,                  // Minimum valid price
    "max_price": 10000.0,               // Maximum valid price
    "enable_outlier_detection": true,   // Enable outlier detection
    "outlier_threshold": 3.0            // Outlier threshold (std deviations)
  }
}
```

### Environment Variables
Override configuration with environment variables:
- `STOCK_API_RATE_LIMIT`: API rate limit delay
- `STOCK_API_MAX_RETRIES`: Maximum retry attempts
- `STOCK_MIN_TRADING_DAYS`: Minimum trading days
- `STOCK_TOP_N`: Number of top stocks
- `STOCK_LOG_LEVEL`: Logging level
- `STOCK_LOG_FILE`: Log file path
- `STOCK_OUTPUT_DIR`: Output directory

## Module Overview

### `config.py`
Configuration management with validation and environment variable support.

### `stock_data_fetcher.py`
Enhanced data fetching with retry logic, rate limiting, and parallel processing.

### `data_validator.py`
Comprehensive data validation with quality scoring and cleaning.

### `stock_analyzer.py`
Advanced analysis with amplitude calculation and statistical metrics.

### `utils.py`
Utility functions for logging, formatting, and file operations.

### `get_stock_list_enhanced.py`
Main system orchestrator with CLI interface.

## Output

The system generates results in multiple formats:

### CSV Output (default)
```csv
股票代码,股票名称,日均振幅(%),交易天数,波动率(%),平均价格,总收益率(%)
000001,平安银行,3.45,20,2.1,12.34,5.67
```

### Excel Output
Formatted Excel file with the same data structure.

### JSON Output
```json
[
  {
    "股票代码": "000001",
    "股票名称": "平安银行",
    "日均振幅(%)": 3.45,
    "交易天数": 20,
    "波动率(%)": 2.1,
    "平均价格": 12.34,
    "总收益率(%)": 5.67
  }
]
```

## Backward Compatibility

The enhanced system maintains compatibility with the original script:

```python
# Original functions still work
from get_stock_list_enhanced import test_stock_data_retrieval, get_top_10_amplitude_stocks

# Test data retrieval
test_stock_data_retrieval()

# Get amplitude analysis
results = get_top_10_amplitude_stocks(year=2024, month=12)
```

## Error Handling

The system includes comprehensive error handling:

- **Network Errors**: Automatic retry with exponential backoff
- **Data Validation Errors**: Graceful handling with detailed logging
- **Configuration Errors**: Clear error messages with suggestions
- **Memory Errors**: Memory usage monitoring and warnings

## Performance

### Optimizations
- Parallel data fetching with configurable concurrency
- Efficient data processing with pandas vectorization
- Memory usage monitoring and optimization
- Progress tracking with ETA calculation

### Monitoring
- Real-time progress reporting
- Session statistics tracking
- API call success/failure rates
- Memory usage monitoring

## Logging

### Standard Logging
```
2024-01-15 10:30:45 - StockDataFetcher - INFO - Successfully fetched 4521 stocks
```

### JSON Logging (with --json-logs)
```json
{
  "timestamp": "2024-01-15T10:30:45.123456",
  "level": "INFO",
  "logger": "StockDataFetcher",
  "message": "Successfully fetched 4521 stocks",
  "module": "stock_data_fetcher",
  "function": "get_stock_list",
  "line": 78
}
```

## Contributing

1. Follow PEP 8 style guidelines
2. Add type hints to all functions
3. Include comprehensive docstrings
4. Add unit tests for new features
5. Update documentation as needed

## License

This project is licensed under the MIT License.

## Changelog

### v2.0.0 (Enhanced Version)
- Implemented robust error handling
- Added configuration management
- Enhanced data validation
- Improved logging system
- Added multiple output formats
- Implemented parallel processing
- Added comprehensive documentation
