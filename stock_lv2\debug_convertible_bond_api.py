"""
调试可转债API接口
"""

import akshare as ak
import pandas as pd

def 测试可转债API():
    """测试akshare的可转债相关API"""
    print("测试akshare可转债API...")
    
    try:
        # 测试获取可转债列表
        print("\n1. 测试获取可转债列表...")
        可转债列表 = ak.bond_cb_jsl()
        print(f"获取到 {len(可转债列表)} 只可转债")
        print("列名:", list(可转债列表.columns))
        print("前5行数据:")
        print(可转债列表.head())
        
        if len(可转债列表) > 0:
            # 获取第一只可转债的代码
            第一只代码 = 可转债列表.iloc[0]['bond_id']
            print(f"\n2. 测试获取可转债 {第一只代码} 的历史数据...")
            
            # 尝试不同的历史数据API
            try:
                历史数据1 = ak.bond_cb_redeem_jsl(symbol=第一只代码)
                print(f"bond_cb_redeem_jsl: {len(历史数据1)} 行")
                if not 历史数据1.empty:
                    print("列名:", list(历史数据1.columns))
                    print("前3行:")
                    print(历史数据1.head(3))
            except Exception as e:
                print(f"bond_cb_redeem_jsl 失败: {e}")
            
            try:
                历史数据2 = ak.bond_cb_jsl_hist(symbol=第一只代码)
                print(f"bond_cb_jsl_hist: {len(历史数据2)} 行")
                if not 历史数据2.empty:
                    print("列名:", list(历史数据2.columns))
                    print("前3行:")
                    print(历史数据2.head(3))
            except Exception as e:
                print(f"bond_cb_jsl_hist 失败: {e}")
            
            # 尝试其他可能的API
            try:
                历史数据3 = ak.bond_cb_profile_jsl(symbol=第一只代码)
                print(f"bond_cb_profile_jsl: {len(历史数据3)} 行")
                if not 历史数据3.empty:
                    print("列名:", list(历史数据3.columns))
                    print("前3行:")
                    print(历史数据3.head(3))
            except Exception as e:
                print(f"bond_cb_profile_jsl 失败: {e}")
        
        # 测试其他可转债相关API
        print("\n3. 测试其他可转债API...")
        try:
            可转债价格 = ak.bond_cb_jsl()
            print(f"bond_cb_jsl (价格数据): {len(可转债价格)} 行")
            if not 可转债价格.empty:
                print("列名:", list(可转债价格.columns))
        except Exception as e:
            print(f"bond_cb_jsl 失败: {e}")
            
    except Exception as e:
        print(f"API测试失败: {e}")

if __name__ == "__main__":
    测试可转债API()
