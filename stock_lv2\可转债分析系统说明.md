# 可转债市场数据分析系统

## 概述

这是一个基于Context7增强架构模式开发的可转债市场数据分析系统，提供可转债日均振幅分析功能。系统采用模块化设计，具备强大的错误处理、数据验证和配置管理能力。

## 主要功能

### 🎯 核心分析功能
- **可转债表现指标分析**: 基于当日涨跌幅计算活跃度指标
- **前N名筛选**: 自动筛选表现最活跃的可转债标的
- **数据质量评估**: 对每只可转债的数据质量进行评分
- **多维度指标**: 包含价格、转股溢价率、收益率等多项指标

### 🛡️ 技术特性
- **Context7增强架构**: 借鉴Context7的模块化和配置管理理念
- **智能重试机制**: 指数退避重试逻辑，提高数据获取成功率
- **数据验证**: 全面的数据质量检查和清洗
- **结构化日志**: 支持JSON格式日志，便于监控和调试
- **多格式输出**: 支持CSV、Excel等多种输出格式

## 快速开始

### 安装依赖
```bash
pip install akshare pandas numpy
```

### 基本使用

#### 1. 运行测试
```bash
python convertible_bond_analyzer.py --test
```

#### 2. 分析可转债表现
```bash
# 分析前10名最活跃的可转债
python convertible_bond_analyzer.py

# 分析前15名，限制处理20只可转债
python convertible_bond_analyzer.py --max-bonds 20

# 启用调试日志
python convertible_bond_analyzer.py --log-level DEBUG
```

#### 3. 自定义配置
```bash
# 使用自定义配置文件
python convertible_bond_analyzer.py --config convertible_bond_config.json

# 指定输出目录
python convertible_bond_analyzer.py --output-dir /path/to/output
```

## 输出结果说明

### 控制台输出
```
2025年6月可转债日均振幅前10名:
====================================================================================================
 可转债代码 可转债名称  日均振幅(%)  交易天数   平均价格  价格波动率(%)  总收益率(%)  数据质量评分
404003  鸿达退债    87.19     1   2.48    -19.06   -87.19    1.00
404004  汇车退债    33.35     1  56.14    -15.79    33.35    1.00
123162  东杰转债    20.00     1 171.60     -2.44    20.00    1.00
```

### 字段说明
- **可转债代码**: 可转债的唯一标识代码
- **可转债名称**: 可转债的中文名称
- **日均振幅(%)**: 基于当日涨跌幅绝对值的活跃度指标
- **交易天数**: 数据覆盖的交易天数（当前版本为1天）
- **平均价格**: 可转债当前价格
- **价格波动率(%)**: 转股溢价率（反映转股价值）
- **总收益率(%)**: 当日涨跌幅
- **数据质量评分**: 数据质量评分（0-1之间）

### CSV文件输出
系统会自动生成带时间戳的CSV文件，保存在`output`目录下：
```
output/analysis_20250802_204402/可转债振幅分析_前10名_20250802_204402.csv
```

## 配置说明

### 默认配置文件 (convertible_bond_config.json)
```json
{
  "api": {
    "rate_limit_delay": 0.1,        // API调用间隔（秒）
    "max_retries": 3,               // 最大重试次数
    "retry_backoff_factor": 2.0     // 重试退避因子
  },
  "analysis": {
    "top_n_stocks": 10,             // 返回前N名数量
    "amplitude_threshold": 0.0      // 活跃度阈值
  },
  "output": {
    "save_formats": ["csv", "excel"], // 输出格式
    "include_timestamp": true        // 文件名包含时间戳
  }
}
```

### 环境变量配置
可以通过环境变量覆盖配置：
```bash
export STOCK_API_RATE_LIMIT=0.2
export STOCK_LOG_LEVEL=DEBUG
export STOCK_OUTPUT_DIR=/custom/output
```

## 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--test` | 仅运行数据获取测试 | - |
| `--year` | 分析年份 | 2025 |
| `--month` | 分析月份 | 6 |
| `--max-bonds` | 最大处理可转债数量 | 无限制 |
| `--config` | 配置文件路径 | 默认配置 |
| `--output-dir` | 输出目录 | output |
| `--log-level` | 日志级别 | INFO |
| `--json-logs` | 启用JSON日志 | false |

## 程序化使用

### 基本用法
```python
from convertible_bond_analyzer import 可转债分析系统

# 初始化系统
系统 = 可转债分析系统()

# 运行测试
测试结果 = 系统.测试数据获取()

# 获取分析结果
结果 = 系统.获取前N名振幅可转债(年份=2025, 月份=6, 最大处理数量=20)
print(结果)
```

### 向后兼容函数
```python
# 兼容原有接口
from convertible_bond_analyzer import 测试可转债数据获取, 获取前10名振幅可转债

# 测试数据获取
测试可转债数据获取()

# 获取分析结果
结果 = 获取前10名振幅可转债(年份=2025, 月份=6)
```

## 数据来源

系统使用akshare库获取可转债市场数据：
- **数据源**: 集思录可转债数据
- **更新频率**: 实时市场数据
- **数据字段**: 包含价格、涨跌幅、转股溢价率等关键指标

## 技术架构

### 模块结构
```
convertible_bond_analyzer.py
├── 可转债配置                    # 配置管理
├── 可转债数据获取器              # 数据获取与重试
├── 可转债数据验证器              # 数据验证与清洗
├── 可转债分析器                  # 指标计算与分析
└── 可转债分析系统                # 主系统协调器
```

### 设计特点
- **模块化设计**: 各组件职责清晰，便于维护和扩展
- **配置驱动**: 通过配置文件控制系统行为
- **错误恢复**: 智能重试和降级处理
- **数据质量**: 全面的数据验证和质量评估
- **可观测性**: 详细的日志记录和性能监控

## 性能特点

### 处理能力
- **数据获取**: 支持批量获取，自动限流
- **处理速度**: 单次分析通常在1秒内完成
- **内存使用**: 优化的数据处理，内存占用较低
- **成功率**: 通过重试机制确保高成功率

### 监控指标
- **API调用统计**: 总请求数、成功率、重试次数
- **数据质量**: 验证通过率、数据完整性
- **处理性能**: 处理时间、内存使用情况

## 故障排除

### 常见问题

#### 1. 网络连接问题
```
错误: 获取可转债市场数据失败
解决: 检查网络连接，或增加重试次数
```

#### 2. 数据验证失败
```
错误: 缺少必需列
解决: 检查akshare版本，确保API兼容性
```

#### 3. 配置文件错误
```
错误: 配置文件加载失败
解决: 检查JSON格式，确保文件路径正确
```

### 调试技巧

#### 启用详细日志
```bash
python convertible_bond_analyzer.py --log-level DEBUG --json-logs
```

#### 测试数据获取
```bash
python convertible_bond_analyzer.py --test
```

#### 限制处理数量
```bash
python convertible_bond_analyzer.py --max-bonds 5
```

## 扩展开发

### 添加新指标
1. 在`可转债分析器`类中添加计算方法
2. 更新`分析可转债`方法
3. 修改输出格式

### 自定义数据源
1. 继承`可转债数据获取器`类
2. 重写数据获取方法
3. 更新数据验证规则

### 新增输出格式
1. 在`utils.py`中添加格式化函数
2. 更新配置文件
3. 修改保存逻辑

## 版本历史

### v1.0.0 (2025-08-02)
- 初始版本发布
- 基于Context7架构设计
- 支持可转债市场数据分析
- 实现模块化架构和配置管理
- 添加数据验证和质量评估
- 支持多种输出格式

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件至开发团队
